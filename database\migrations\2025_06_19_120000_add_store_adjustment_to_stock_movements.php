<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'store_adjustment' and 'return_to_supplier' to the source enum in stock_movements table
        DB::statement("ALTER TABLE stock_movements MODIFY COLUMN source ENUM('supplier', 'store', 'adjustment', 'distribution', 'store_adjustment', 'return_to_supplier')");
        
        // Ensure the table has all necessary indexes for performance
        Schema::table('stock_movements', function (Blueprint $table) {
            // Add index for store-level movements if not exists
            if (!$this->indexExists('stock_movements', 'stock_movements_reference_type_reference_id_index')) {
                $table->index(['reference_type', 'reference_id'], 'stock_movements_reference_type_reference_id_index');
            }
            
            // Add index for source filtering if not exists
            if (!$this->indexExists('stock_movements', 'stock_movements_source_index')) {
                $table->index('source', 'stock_movements_source_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'store_adjustment' and 'return_to_supplier' from the source enum
        DB::statement("ALTER TABLE stock_movements MODIFY COLUMN source ENUM('supplier', 'store', 'adjustment', 'distribution')");
        
        // Remove the indexes we added
        Schema::table('stock_movements', function (Blueprint $table) {
            if ($this->indexExists('stock_movements', 'stock_movements_reference_type_reference_id_index')) {
                $table->dropIndex('stock_movements_reference_type_reference_id_index');
            }
            
            if ($this->indexExists('stock_movements', 'stock_movements_source_index')) {
                $table->dropIndex('stock_movements_source_index');
            }
        });
    }

    /**
     * Check if an index exists on a table.
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $indexInfo) {
            if ($indexInfo->Key_name === $index) {
                return true;
            }
        }
        return false;
    }
};
