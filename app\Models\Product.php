<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Product extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
    ];

    /**
     * Get the warehouse stock for the product.
     */
    public function warehouseStock()
    {
        return $this->hasMany(WarehouseStock::class);
    }

    /**
     * Get the distributions for the product.
     */
    public function distributions()
    {
        return $this->hasMany(Distribution::class);
    }

    /**
     * Get the store stock for the product.
     */
    public function storeStock()
    {
        return $this->hasMany(StoreStock::class);
    }

    /**
     * Get the stock opname for the product.
     */
    public function stockOpname()
    {
        return $this->hasMany(StockOpname::class);
    }

    /**
     * Get the supplier deliveries for the product.
     */
    public function supplierDeliveries()
    {
        return $this->hasMany(SupplierDelivery::class);
    }

    /**
     * Get the most recent supplier who delivered this product.
     */
    public function getMostRecentSupplier()
    {
        return $this->supplierDeliveries()
            ->with('supplier')
            ->where('status', 'received')
            ->orderBy('received_date', 'desc')
            ->first()?->supplier;
    }

    /**
     * Get all suppliers who have delivered this product, ordered by most recent.
     */
    public function getSuppliersWhoDelivered()
    {
        return Supplier::whereHas('supplierDeliveries', function ($query) {
            $query->where('product_id', $this->id)
                  ->where('status', 'received');
        })->withCount(['supplierDeliveries as delivery_count' => function ($query) {
            $query->where('product_id', $this->id)
                  ->where('status', 'received');
        }])->orderBy('delivery_count', 'desc')->get();
    }

    /**
     * Get the returns for the product.
     */
    public function returns()
    {
        return $this->hasMany(ReturnModel::class);
    }

    /**
     * Get current warehouse stock quantity
     * Always queries fresh data from database
     * Only counts central warehouse stock (store_id = null)
     */
    public function getCurrentWarehouseStock()
    {
        // Use fresh query to avoid caching issues
        // Only count central warehouse stock (store_id = null)
        return \App\Models\WarehouseStock::where('product_id', $this->id)
            ->whereNull('store_id')
            ->sum('quantity');
    }

    /**
     * Get pending distributions quantity (unconfirmed)
     * Always queries fresh data from database
     */
    public function getPendingDistributionsQuantity()
    {
        // Use fresh query to avoid caching issues
        return \App\Models\Distribution::where('product_id', $this->id)
            ->where('confirmed', false)
            ->sum('quantity');
    }

    /**
     * Get available stock for new distributions
     * Available = Current warehouse stock - Pending distributions
     */
    public function getAvailableStock()
    {
        return $this->getCurrentWarehouseStock() - $this->getPendingDistributionsQuantity();
    }

    /**
     * Get committed stock (pending distributions)
     */
    public function getCommittedStock()
    {
        return $this->getPendingDistributionsQuantity();
    }

    /**
     * Get confirmed distributions count
     */
    public function getConfirmedDistributionsCount()
    {
        return $this->distributions()->where('confirmed', true)->count();
    }

    /**
     * Get pending distributions count
     */
    public function getPendingDistributionsCount()
    {
        return $this->distributions()->where('confirmed', false)->count();
    }

    /**
     * Get detailed stock information for display
     */
    public function getStockSummary()
    {
        $warehouseStock = $this->getCurrentWarehouseStock();
        $pendingDistributions = $this->getPendingDistributionsQuantity();
        $availableStock = $warehouseStock - $pendingDistributions;
        $storeStock = $this->storeStock()->sum('quantity');
        $confirmedDistributions = $this->distributions()->where('confirmed', true)->sum('quantity');

        return [
            'warehouse_stock' => $warehouseStock,
            'pending_distributions' => $pendingDistributions,
            'available_stock' => $availableStock,
            'store_stock' => $storeStock,
            'confirmed_distributions' => $confirmedDistributions,
            'total_distributed' => $confirmedDistributions,
            'has_pending_distributions' => $pendingDistributions > 0,
            'can_reduce_stock' => $availableStock > 0,
        ];
    }

    /**
     * Check if stock can be reduced by specified amount
     */
    public function canReduceStock($amount)
    {
        return $this->getAvailableStock() >= $amount;
    }

    /**
     * Check if stock can be set to specified amount
     */
    public function canSetStock($amount)
    {
        return $amount >= $this->getPendingDistributionsQuantity();
    }

    /**
     * Get reasons why product cannot be deleted
     */
    public function getDeletionBlockingReasons()
    {
        $reasons = [];

        if ($this->warehouseStock()->count() > 0) {
            $reasons[] = [
                'type' => 'warehouse_stock',
                'message' => 'Memiliki stok gudang',
                'count' => $this->warehouseStock()->count(),
                'quantity' => $this->getCurrentWarehouseStock()
            ];
        }

        if ($this->distributions()->count() > 0) {
            $reasons[] = [
                'type' => 'distributions',
                'message' => 'Memiliki riwayat distribusi',
                'count' => $this->distributions()->count(),
                'pending' => $this->getPendingDistributionsQuantity()
            ];
        }

        if ($this->storeStock()->count() > 0) {
            $reasons[] = [
                'type' => 'store_stock',
                'message' => 'Memiliki stok di toko',
                'count' => $this->storeStock()->count(),
                'quantity' => $this->storeStock()->sum('quantity')
            ];
        }

        if ($this->stockOpname()->count() > 0) {
            $reasons[] = [
                'type' => 'stock_opname',
                'message' => 'Memiliki riwayat stock opname',
                'count' => $this->stockOpname()->count()
            ];
        }

        return $reasons;
    }

    /**
     * Check if product can be deleted
     */
    public function canBeDeleted()
    {
        return empty($this->getDeletionBlockingReasons());
    }
}
