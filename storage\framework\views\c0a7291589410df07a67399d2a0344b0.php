

<?php $__env->startSection('title', 'Dashboard Toko - Indah Berkah Abadi'); ?>

<?php $__env->startSection('content'); ?>
<div class="user-dashboard-layout-with-analytics">
    <!-- Analytics Sidebar Toggle Button -->
    <button class="user-dashboard-analytics-toggle-btn" onclick="toggleUserAnalyticsSidebar()">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <span>Analisis</span>
    </button>

    <!-- Main Dashboard Content -->
    <div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Dashboard <?php echo e($storeInfo['name']); ?></h1>
            <p class="user-dashboard-header-subtitle">Kelola inventori dan operasi toko Anda</p>
        </div>
        <div class="user-dashboard-header-actions">
            <a href="<?php echo e(route('user.inventory')); ?>" class="user-dashboard-btn-primary user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                Stok Toko
            </a>
            <a href="<?php echo e(route('user.deliveries')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Terima Barang
            </a>

        </div>
    </div>

    <!-- Store Info Card -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-lg p-4 sm:p-6 text-white">
        <div class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <div class="flex-1">
                <h2 class="text-lg sm:text-xl font-semibold"><?php echo e($storeInfo['name']); ?></h2>
                <div class="mt-2 space-y-1">
                    <p class="text-green-100 text-sm">Kode Toko: <?php echo e($storeInfo['code']); ?></p>
                    <p class="text-green-100 text-sm">Manager: <?php echo e($storeInfo['manager']); ?></p>
                </div>
            </div>
            <div class="flex-shrink-0">
                <div class="bg-white bg-opacity-20 rounded-lg p-3 sm:p-4 text-center">
                    <div class="text-xl sm:text-2xl font-bold text-black"><?php echo e(date('d')); ?></div>
                    <div class="text-xs sm:text-sm text-black"><?php echo e(date('M Y')); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Stats Cards -->
    <div class="user-dashboard-stat-grid">
        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Total Produk</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e(number_format($stats['total_products'])); ?></div>
            <div class="user-dashboard-stat-description">Jenis produk</div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Total Stok</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e(number_format($stats['total_stock'])); ?></div>
            <div class="user-dashboard-stat-description">Unit produk</div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Distribusi Pending</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e($stats['pending_distributions']); ?></div>
            <div class="user-dashboard-stat-description">Distribusi</div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Stok Rendah</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e($stats['low_stock_items']); ?></div>
            <div class="user-dashboard-stat-description">Produk</div>
        </div>
    </div>

    <!-- Pending Deliveries and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Pending Distributions -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Distribusi Pending</h2>
                <p class="user-dashboard-card-description">Distribusi dari gudang yang menunggu konfirmasi</p>
            </div>
            <div class="user-dashboard-card-content">
                <div class="user-dashboard-list">
                    <?php $__empty_1 = true; $__currentLoopData = $pendingDeliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="user-dashboard-list-item">
                        <div class="user-dashboard-list-item-content">
                            <div class="user-dashboard-list-item-main">
                                <div class="flex items-center space-x-2">
                                    <span class="user-dashboard-list-item-title"><?php echo e($delivery['product_name']); ?></span>
                                </div>
                                <p class="user-dashboard-list-item-subtitle"><?php echo e($delivery['quantity']); ?></p>
                                <p class="user-dashboard-list-item-meta">Tanggal: <?php echo e($delivery['date_distributed']); ?></p>
                            </div>
                            <div class="user-dashboard-list-item-actions">
                                <span class="user-dashboard-badge-<?php echo e($delivery['status'] == 'Dikonfirmasi' ? 'green' : 'orange'); ?>">
                                    <?php echo e($delivery['status']); ?>

                                </span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="user-dashboard-empty-state">
                        <svg class="user-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        <p class="user-dashboard-empty-title">Tidak ada distribusi pending</p>
                    </div>
                    <?php endif; ?>
                </div>
                <?php if(count($pendingDeliveries) > 0): ?>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <a href="<?php echo e(route('user.deliveries')); ?>" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                        Lihat semua distribusi →
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Menu Cepat</h2>
                <p class="user-dashboard-card-description">Operasi yang sering digunakan</p>
            </div>
            <div class="user-dashboard-card-content">
                <div class="user-dashboard-quick-actions">
                    <?php $__currentLoopData = $quickActions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route($action['route'])); ?>" class="user-dashboard-quick-action">
                        <div class="user-dashboard-quick-action-icon bg-<?php echo e($action['color']); ?>-100">
                            <?php if($action['icon'] == 'package'): ?>
                                <svg class="w-6 h-6 text-<?php echo e($action['color']); ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            <?php elseif($action['icon'] == 'check-circle'): ?>
                                <svg class="w-6 h-6 text-<?php echo e($action['color']); ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            <?php elseif($action['icon'] == 'clipboard'): ?>
                                <svg class="w-6 h-6 text-<?php echo e($action['color']); ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            <?php elseif($action['icon'] == 'truck'): ?>
                                <svg class="w-6 h-6 text-<?php echo e($action['color']); ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                </svg>
                            <?php else: ?>
                                <svg class="w-6 h-6 text-<?php echo e($action['color']); ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <h3 class="user-dashboard-quick-action-title"><?php echo e($action['title']); ?></h3>
                        <p class="user-dashboard-quick-action-description"><?php echo e($action['description']); ?></p>
                    </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Analytics Sidebar -->
    <?php echo $__env->make('user.components.analytics-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/user/dashboard.blade.php ENDPATH**/ ?>