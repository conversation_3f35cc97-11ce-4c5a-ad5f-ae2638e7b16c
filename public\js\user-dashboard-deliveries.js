/**
 * User Dashboard Deliveries JavaScript
 * Handles modal interactions and form submissions for the deliveries table
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize modal functionality
    initializeReturnModal();
    initializeConfirmationModal();
});

/**
 * Initialize confirmation modal functionality
 */
function initializeConfirmationModal() {
    const modal = document.getElementById('confirmationModal');

    if (modal) {
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeConfirmationModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                closeConfirmationModal();
            }
        });
    }
}

/**
 * Initialize return modal functionality
 */
function initializeReturnModal() {
    const modal = document.getElementById('returnModal');

    if (modal) {
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeReturnModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                closeReturnModal();
            }
        });
    }
}

/**
 * Open confirmation modal with delivery data
 * @param {string} deliveryId - The delivery ID
 * @param {string} productName - The product name
 * @param {number} sentQuantity - Quantity that was sent
 * @param {string} productId - The product ID
 */
function openConfirmationModal(deliveryId, productName, sentQuantity, productId) {
    const modal = document.getElementById('confirmationModal');
    const form = document.getElementById('confirmationForm');
    const productNameElement = document.getElementById('confirmProductName');
    const sentQuantityElement = document.getElementById('sentQuantity');
    const receivedQuantityInput = document.getElementById('received_quantity');

    if (!modal || !form) {
        console.error('Confirmation modal or form not found');
        return;
    }

    // Set form action
    form.action = `/user/deliveries/${deliveryId}/confirm`;

    // Set product information
    if (productNameElement) {
        productNameElement.textContent = productName;
    }

    if (sentQuantityElement) {
        sentQuantityElement.textContent = `${sentQuantity} unit`;
    }

    if (receivedQuantityInput) {
        receivedQuantityInput.max = sentQuantity;
        receivedQuantityInput.value = sentQuantity; // Default to full quantity
    }

    // Show modal
    modal.classList.add('active');

    // Focus on quantity input
    if (receivedQuantityInput) {
        setTimeout(() => {
            receivedQuantityInput.focus();
            receivedQuantityInput.select();
        }, 300);
    }
}

/**
 * Close confirmation modal
 */
function closeConfirmationModal() {
    const modal = document.getElementById('confirmationModal');
    const form = document.getElementById('confirmationForm');

    if (modal) {
        modal.classList.remove('active');
    }

    if (form) {
        form.reset();
    }
}

/**
 * Open return modal with distribution data
 * @param {string} distributionId - The distribution ID
 * @param {string} productName - The product name
 * @param {number} maxQuantity - Maximum returnable quantity
 * @param {string} productId - The product ID
 */
function openReturnModal(distributionId, productName, maxQuantity, productId) {
    const modal = document.getElementById('returnModal');
    const productNameElement = document.getElementById('returnProductName');
    const quantityInput = document.getElementById('return_quantity');
    const maxQuantityElement = document.getElementById('maxReturnQuantity');
    const distributionIdInput = document.getElementById('distribution_id');
    const productIdInput = document.getElementById('product_id');
    const reasonSelect = document.getElementById('return_reason');
    const descriptionTextarea = document.getElementById('return_description');

    if (!modal) {
        console.error('Return modal not found');
        return;
    }

    // Set the product name and maximum quantity for the return
    if (productNameElement) {
        productNameElement.textContent = productName;
    }
    
    if (maxQuantityElement) {
        maxQuantityElement.textContent = maxQuantity;
    }
    
    if (quantityInput) {
        quantityInput.max = maxQuantity;
        quantityInput.value = Math.min(1, maxQuantity);
    }
    
    if (distributionIdInput) {
        distributionIdInput.value = distributionId;
    }
    
    if (productIdInput) {
        productIdInput.value = productId;
    }

    // Pre-select "Kekurangan Pengiriman" as the default reason for shortage returns
    if (reasonSelect) {
        reasonSelect.value = 'shortage';
    }

    // Pre-fill description with helpful text
    if (descriptionTextarea) {
        descriptionTextarea.value = `Kekurangan pengiriman untuk produk ${productName}. Jumlah yang tidak diterima: ${maxQuantity} unit.`;
    }

    // Show modal
    modal.classList.add('active');
    
    // Focus on quantity input
    if (quantityInput) {
        setTimeout(() => {
            quantityInput.focus();
            quantityInput.select();
        }, 300);
    }
}

/**
 * Close return modal and reset form
 */
function closeReturnModal() {
    const modal = document.getElementById('returnModal');
    const form = document.getElementById('returnForm');
    
    if (modal) {
        modal.classList.remove('active');
    }
    
    if (form) {
        // Reset form after modal animation completes
        setTimeout(() => {
            form.reset();
            
            // Reset hidden fields
            const distributionIdInput = document.getElementById('distribution_id');
            const productIdInput = document.getElementById('product_id');
            
            if (distributionIdInput) {
                distributionIdInput.value = '';
            }
            
            if (productIdInput) {
                productIdInput.value = '';
            }
            
            // Reset date to today
            const dateInput = document.getElementById('return_date');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.value = today;
            }
        }, 300);
    }
}

/**
 * Validate return form before submission
 */
function validateReturnForm() {
    const form = document.getElementById('returnForm');
    const quantityInput = document.getElementById('return_quantity');
    const reasonSelect = document.getElementById('return_reason');
    const descriptionTextarea = document.getElementById('return_description');
    
    if (!form) return false;
    
    let isValid = true;
    const errors = [];
    
    // Validate quantity
    if (quantityInput) {
        const quantity = parseInt(quantityInput.value);
        const maxQuantity = parseInt(quantityInput.max);
        
        if (isNaN(quantity) || quantity < 1) {
            errors.push('Jumlah retur harus minimal 1');
            isValid = false;
        } else if (quantity > maxQuantity) {
            errors.push(`Jumlah retur tidak boleh melebihi ${maxQuantity}`);
            isValid = false;
        }
    }
    
    // Validate reason
    if (reasonSelect && !reasonSelect.value) {
        errors.push('Alasan retur wajib dipilih');
        isValid = false;
    }
    
    // Validate description
    if (descriptionTextarea && !descriptionTextarea.value.trim()) {
        errors.push('Deskripsi detail wajib diisi');
        isValid = false;
    }
    
    // Show errors if any
    if (!isValid) {
        alert('Terdapat kesalahan:\n' + errors.join('\n'));
    }
    
    return isValid;
}

// Add form validation on submit
document.addEventListener('DOMContentLoaded', function() {
    const returnForm = document.getElementById('returnForm');
    
    if (returnForm) {
        returnForm.addEventListener('submit', function(e) {
            if (!validateReturnForm()) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitButton = returnForm.querySelector('button[type="submit"]');
            if (submitButton) {
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Memproses...';
                submitButton.disabled = true;
                
                // Re-enable button after 5 seconds as fallback
                setTimeout(() => {
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }, 5000);
            }
        });
    }
});

/**
 * Handle table row hover effects
 */
document.addEventListener('DOMContentLoaded', function() {
    const tableRows = document.querySelectorAll('.user-dashboard-deliveries-table-row');
    
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f9fafb';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#ffffff';
        });
    });
});

/**
 * Handle responsive table scrolling
 */
document.addEventListener('DOMContentLoaded', function() {
    const tableContainer = document.querySelector('.user-dashboard-deliveries-table-container');
    
    if (tableContainer) {
        // Add scroll indicators for mobile
        function updateScrollIndicators() {
            const scrollLeft = tableContainer.scrollLeft;
            const scrollWidth = tableContainer.scrollWidth;
            const clientWidth = tableContainer.clientWidth;
            
            // Add classes for styling scroll indicators if needed
            if (scrollLeft > 0) {
                tableContainer.classList.add('scrolled-left');
            } else {
                tableContainer.classList.remove('scrolled-left');
            }
            
            if (scrollLeft < scrollWidth - clientWidth) {
                tableContainer.classList.add('can-scroll-right');
            } else {
                tableContainer.classList.remove('can-scroll-right');
            }
        }
        
        tableContainer.addEventListener('scroll', updateScrollIndicators);
        window.addEventListener('resize', updateScrollIndicators);
        updateScrollIndicators(); // Initial check
    }
});

/**
 * Utility function to format numbers
 * @param {number} num - Number to format
 * @returns {string} Formatted number
 */
function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

/**
 * Utility function to format dates
 * @param {string} dateString - Date string to format
 * @returns {string} Formatted date
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
    });
}

// Export functions for global access
window.openReturnModal = openReturnModal;
window.closeReturnModal = closeReturnModal;
