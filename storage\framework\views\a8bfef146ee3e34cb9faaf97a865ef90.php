


<div class="user-dashboard-analytics-sidebar">
    <!-- Analytics Header -->
    <div class="user-dashboard-analytics-header">
        <div class="flex items-center justify-between">
            <h3 class="user-dashboard-analytics-title">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Perbandingan Distribusi
            </h3>
            <button class="user-dashboard-analytics-toggle" onclick="toggleUserAnalyticsSidebar()">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Info Section -->
    <div class="user-dashboard-analytics-info">
        <p class="text-sm text-gray-600">Data distribusi 30 hari terakhir untuk toko Anda</p>
    </div>

    <!-- Store Summary Statistics -->
    <div class="user-dashboard-analytics-summary">
        <h4 class="user-dashboard-analytics-section-title">Ringkasan</h4>

        <div class="user-dashboard-analytics-stat-grid">
            <!-- Total Distributions -->
            <div class="user-dashboard-analytics-stat-item">
                <div class="user-dashboard-analytics-stat-value"><?php echo e(number_format($analytics['summary']['total_distributions'] ?? 0)); ?></div>
                <div class="user-dashboard-analytics-stat-label">Total Distribusi</div>
            </div>

            <!-- Total Sent -->
            <div class="user-dashboard-analytics-stat-item">
                <div class="user-dashboard-analytics-stat-value"><?php echo e(number_format($analytics['summary']['total_sent'] ?? 0)); ?></div>
                <div class="user-dashboard-analytics-stat-label">Jumlah Dikirim</div>
            </div>

            <!-- Total Received -->
            <div class="user-dashboard-analytics-stat-item">
                <div class="user-dashboard-analytics-stat-value"><?php echo e(number_format($analytics['summary']['total_received'] ?? 0)); ?></div>
                <div class="user-dashboard-analytics-stat-label">Jumlah Diterima</div>
            </div>

            <!-- Difference -->
            <div class="user-dashboard-analytics-stat-item">
                <div class="user-dashboard-analytics-stat-value <?php echo e(($analytics['summary']['total_difference'] ?? 0) < 0 ? 'text-red-600' : (($analytics['summary']['total_difference'] ?? 0) > 0 ? 'text-orange-600' : 'text-green-600')); ?>">
                    <?php echo e(($analytics['summary']['total_difference'] ?? 0) >= 0 ? '+' : ''); ?><?php echo e(number_format($analytics['summary']['total_difference'] ?? 0)); ?>

                </div>
                <div class="user-dashboard-analytics-stat-label">Selisih</div>
            </div>
        </div>
    </div>

    <!-- Recent Distributions for This Store -->
    <div class="user-dashboard-analytics-section">
        <h4 class="user-dashboard-analytics-section-title">Distribusi Terbaru</h4>

        <div class="user-dashboard-analytics-distributions-list">
            <?php $__empty_1 = true; $__currentLoopData = $analytics['recent_distributions'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $distribution): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="user-dashboard-analytics-distribution-item">
                <div class="user-dashboard-analytics-distribution-header">
                    <span class="user-dashboard-analytics-distribution-product"><?php echo e($distribution['product_name']); ?></span>
                    <span class="user-dashboard-analytics-distribution-badge <?php echo e($distribution['difference_type']); ?>">
                        <?php if($distribution['difference'] == 0): ?>
                            ✓
                        <?php else: ?>
                            <?php echo e($distribution['difference'] >= 0 ? '+' : ''); ?><?php echo e($distribution['difference']); ?>

                        <?php endif; ?>
                    </span>
                </div>
                <div class="user-dashboard-analytics-distribution-details">
                    <span class="user-dashboard-analytics-distribution-date"><?php echo e($distribution['formatted_date']); ?></span>
                </div>
                <div class="user-dashboard-analytics-distribution-quantities">
                    <span class="text-xs text-gray-600">
                        Dikirim: <?php echo e($distribution['quantity_sent']); ?> |
                        Diterima: <?php echo e($distribution['quantity_received']); ?>

                    </span>
                </div>
                <?php if($distribution['notes']): ?>
                <div class="user-dashboard-analytics-distribution-notes"><?php echo e(Str::limit($distribution['notes'], 60)); ?></div>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="user-dashboard-analytics-empty">
                <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <p class="text-sm text-gray-500">Belum ada distribusi</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

</div>

<!-- User Analytics Sidebar Overlay -->
<div class="user-dashboard-analytics-overlay" onclick="toggleUserAnalyticsSidebar()"></div>
<?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/user/components/analytics-sidebar.blade.php ENDPATH**/ ?>