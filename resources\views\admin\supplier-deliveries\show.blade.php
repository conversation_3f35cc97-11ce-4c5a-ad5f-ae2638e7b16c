@extends('layouts.admin')

@section('title', 'Detail Pengiriman Supplier - Indah Berkah Abadi')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Detail Pengiriman Supplier</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap pengiriman dari supplier ke gudang</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    @if($delivery->status === 'pending')
                    <button onclick="openReceiveModal('{{ $delivery->id }}', '{{ $delivery->quantity }}')" 
                            class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Terima Pengiriman
                    </button>
                    <button onclick="openCancelModal('{{ $delivery->id }}')"
                            class="admin-dashboard-btn admin-dashboard-btn-danger">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batalkan
                    </button>
                    @elseif(in_array($delivery->status, ['received', 'partial']) && $delivery->max_returnable_quantity > 0)
                    <button onclick="openReturnModal('{{ $delivery->id }}', '{{ $delivery->product->name }}', '{{ $delivery->max_returnable_quantity }}')"
                            class="admin-dashboard-btn admin-dashboard-btn-warning">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                        </svg>
                        Buat Retur
                    </button>
                    @endif
                    <a href="{{ route('admin.supplier-deliveries.index') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Status -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-status-icon 
                        @if($delivery->status === 'pending') admin-dashboard-status-pending
                        @elseif($delivery->status === 'received') admin-dashboard-status-received
                        @elseif($delivery->status === 'partial') admin-dashboard-status-partial
                        @elseif($delivery->status === 'cancelled') admin-dashboard-status-cancelled
                        @else admin-dashboard-status-default
                        @endif">
                        @if($delivery->status === 'pending')
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        @elseif($delivery->status === 'received')
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        @elseif($delivery->status === 'partial')
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        @elseif($delivery->status === 'cancelled')
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        @endif
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">
                        Status: 
                        @if($delivery->status === 'pending') Menunggu Konfirmasi
                        @elseif($delivery->status === 'received') Diterima Lengkap
                        @elseif($delivery->status === 'partial') Diterima Sebagian
                        @elseif($delivery->status === 'cancelled') Dibatalkan
                        @else {{ ucfirst($delivery->status) }}
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if($delivery->status === 'pending')
                        Pengiriman menunggu konfirmasi dari admin gudang
                        @elseif($delivery->status === 'received')
                        Pengiriman telah diterima lengkap pada {{ $delivery->received_date->format('d/m/Y H:i') }}
                        @elseif($delivery->status === 'partial')
                        Pengiriman diterima sebagian pada {{ $delivery->received_date->format('d/m/Y H:i') }}
                        @elseif($delivery->status === 'cancelled')
                        Pengiriman dibatalkan
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Pengiriman</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ID Pengiriman</label>
                        <p class="text-gray-900 font-mono text-sm">{{ $delivery->id }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Pengiriman</label>
                        <p class="text-gray-900">{{ $delivery->delivery_date->format('d/m/Y') }}</p>
                    </div>
                    @if($delivery->received_date)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Diterima</label>
                        <p class="text-gray-900">{{ $delivery->received_date->format('d/m/Y H:i') }}</p>
                    </div>
                    @endif
                    @if($delivery->receivedBy)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Diterima Oleh</label>
                        <p class="text-gray-900">{{ $delivery->receivedBy->name }}</p>
                    </div>
                    @endif
                    @if($delivery->notes)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Catatan</label>
                        <p class="text-gray-900">{{ $delivery->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Supplier Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Supplier</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Supplier</label>
                        <p class="text-gray-900 font-semibold">{{ $delivery->supplier->name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Kontak Person</label>
                        <p class="text-gray-900">{{ $delivery->supplier->contact_person ?? '-' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nomor Telepon</label>
                        <p class="text-gray-900">{{ $delivery->supplier->phone ?? '-' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <p class="text-gray-900">{{ $delivery->supplier->email ?? '-' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status Supplier</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($delivery->supplier->status === 'active') bg-green-100 text-green-800
                            @else bg-red-100 text-red-800
                            @endif">
                            @if($delivery->supplier->status === 'active') Aktif @else Tidak Aktif @endif
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Information -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Produk</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nama Produk</label>
                    <p class="text-gray-900 font-semibold">{{ $delivery->product->name }}</p>
                </div>
                <!-- <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                    <p class="text-gray-900">{{ $delivery->product->category->name ?? 'Tanpa Kategori' }}</p>
                </div> -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Jumlah Dikirim</label>
                    <p class="text-gray-900 font-semibold text-lg">{{ number_format($delivery->quantity) }}</p>
                </div>
                @if($delivery->received_quantity)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Jumlah Diterima</label>
                    <p class="text-gray-900 font-semibold text-lg 
                        @if($delivery->received_quantity == $delivery->quantity) text-green-600
                        @else text-yellow-600
                        @endif">
                        {{ number_format($delivery->received_quantity) }}
                    </p>
                </div>
                @endif
            </div>

            @if($delivery->unit_price)
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Harga Satuan</label>
                        <p class="text-gray-900 font-semibold">Rp {{ number_format($delivery->unit_price, 0, ',', '.') }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Total Harga</label>
                        <p class="text-gray-900 font-semibold text-lg">Rp {{ number_format($delivery->total_price, 0, ',', '.') }}</p>
                    </div>
                    @if($delivery->received_quantity && $delivery->unit_price)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nilai Diterima</label>
                        <p class="text-gray-900 font-semibold text-lg">Rp {{ number_format($delivery->received_quantity * $delivery->unit_price, 0, ',', '.') }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Return Modal -->
<div id="returnModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Buat Retur ke Supplier</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeReturnModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="returnForm" method="POST" action="{{ route('admin.returns.store') }}">
            @csrf
            <!-- Hidden fields to map supplier delivery to return format -->
            <input type="hidden" name="product_id" value="{{ $delivery->product_id }}">
            <input type="hidden" name="supplier_id" value="{{ $delivery->supplier_id }}">
            <input type="hidden" name="return_date" value="{{ date('Y-m-d') }}">
            <div class="admin-dashboard-modal-body">
                <div class="space-y-4">
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-orange-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-orange-800">
                                <p class="font-medium">Retur Produk</p>
                                <p>Buat retur untuk produk <strong id="returnProductName">{{ $delivery->product->name }}</strong> yang memiliki masalah kualitas atau kerusakan.</p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="return_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah Retur (Maksimal: <span id="maxReturnQuantityDisplay">{{ $delivery->max_returnable_quantity ?? 0 }}</span>)
                        </label>
                        <input type="number"
                               id="return_quantity"
                               name="quantity"
                               class="admin-dashboard-input"
                               min="1"
                               max="{{ $delivery->max_returnable_quantity ?? 0 }}"
                               value="1"
                               required>
                    </div>

                    <div>
                        <label for="return_reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Retur <span class="text-red-500">*</span>
                        </label>
                        <select id="return_reason" name="reason" class="admin-dashboard-select" required>
                            <option value="">Pilih alasan retur</option>
                            <option value="damaged">Produk Rusak</option>
                            <option value="defective">Produk Cacat</option>
                            <option value="expired">Produk Kadaluarsa</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div>
                        <label for="return_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Deskripsi Detail
                        </label>
                        <textarea id="return_description"
                                  name="description"
                                  rows="3"
                                  class="admin-dashboard-textarea"
                                  placeholder="Jelaskan detail masalah produk..."
                                  required></textarea>
                    </div>

                    <div>
                        <label for="return_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan Tambahan (Opsional)
                        </label>
                        <textarea id="return_notes"
                                  name="admin_notes"
                                  rows="2"
                                  class="admin-dashboard-textarea"
                                  placeholder="Catatan tambahan untuk supplier..."></textarea>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeReturnModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" id="submitReturnBtn" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    <span id="submitBtnText">Buat Retur</span>
                    <span id="submitBtnLoading" class="hidden">Memproses...</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Receive Modal -->
<div id="receiveModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Terima Pengiriman</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeReceiveModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="receiveForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <label for="received_quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah Diterima (Maksimal: <span id="maxQuantity"></span>)
                        </label>
                        <input type="number" 
                               id="received_quantity" 
                               name="received_quantity" 
                               class="admin-dashboard-input"
                               min="1" 
                               required>
                    </div>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="notes" 
                                  name="notes" 
                                  rows="3" 
                                  class="admin-dashboard-textarea"
                                  placeholder="Tambahkan catatan untuk penerimaan ini..."></textarea>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeReceiveModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Terima Pengiriman
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Cancel Modal -->
<div id="cancelModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Batalkan Pengiriman</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeCancelModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="cancelForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Pembatalan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin membatalkan pengiriman ini? Tindakan ini tidak dapat dibatalkan.
                        </p>
                        <div>
                            <label for="cancel_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Alasan Pembatalan (Opsional)
                            </label>
                            <textarea id="cancel_notes"
                                      name="notes"
                                      rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Jelaskan alasan pembatalan..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeCancelModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">
                    Ya, Batalkan Pengiriman
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function openReceiveModal(deliveryId, maxQuantity) {
    const modal = document.getElementById('receiveModal');
    const form = document.getElementById('receiveForm');
    const quantityInput = document.getElementById('received_quantity');
    const maxQuantitySpan = document.getElementById('maxQuantity');
    
    form.action = `/admin/supplier-deliveries/${deliveryId}/receive`;
    quantityInput.max = maxQuantity;
    quantityInput.value = maxQuantity;
    maxQuantitySpan.textContent = maxQuantity;
    
    modal.classList.add('active');
}

function closeReceiveModal() {
    const modal = document.getElementById('receiveModal');
    modal.classList.remove('active');
    document.getElementById('receiveForm').reset();
}

function openCancelModal(deliveryId) {
    const modal = document.getElementById('cancelModal');
    const form = document.getElementById('cancelForm');
    form.action = `/admin/supplier-deliveries/${deliveryId}/cancel`;
    modal.classList.add('active');
}

function closeCancelModal() {
    const modal = document.getElementById('cancelModal');
    modal.classList.remove('active');
    document.getElementById('cancelForm').reset();
}

function openReturnModal(deliveryId, productName, maxQuantity) {
    const modal = document.getElementById('returnModal');
    const productNameElement = document.getElementById('returnProductName');
    const quantityInput = document.getElementById('return_quantity');

    // Set the product name and maximum quantity for the return
    productNameElement.textContent = productName;
    quantityInput.max = maxQuantity;
    quantityInput.value = Math.min(1, maxQuantity);

    modal.classList.add('active');
}

function closeReturnModal() {
    const modal = document.getElementById('returnModal');
    modal.classList.remove('active');
    document.getElementById('returnForm').reset();
}

// Close modals when clicking outside
document.getElementById('receiveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReceiveModal();
    }
});

document.getElementById('cancelModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCancelModal();
    }
});

document.getElementById('returnModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReturnModal();
    }
});

// Add form submission debugging and loading state
document.getElementById('returnForm').addEventListener('submit', function(e) {
    console.log('Return form submitted');

    // Check if all required fields are filled
    const requiredFields = ['product_id', 'supplier_id', 'quantity', 'reason', 'description', 'return_date'];
    let missingFields = [];

    requiredFields.forEach(field => {
        const input = this.querySelector(`[name="${field}"]`);
        if (!input || !input.value.trim()) {
            missingFields.push(field);
        }
    });

    if (missingFields.length > 0) {
        console.error('Missing required fields:', missingFields);
        alert('Harap lengkapi semua field yang wajib diisi: ' + missingFields.join(', '));
        e.preventDefault();
        return false;
    }

    // Show loading state
    const submitBtn = document.getElementById('submitReturnBtn');
    const submitBtnText = document.getElementById('submitBtnText');
    const submitBtnLoading = document.getElementById('submitBtnLoading');

    submitBtn.disabled = true;
    submitBtnText.classList.add('hidden');
    submitBtnLoading.classList.remove('hidden');

    console.log('Form validation passed, submitting...');
});
</script>
@endpush
@endsection
