<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class StockMovement extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'product_id',
        'type',
        'source',
        'quantity',
        'previous_stock',
        'new_stock',
        'reference_type',
        'reference_id',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'previous_stock' => 'integer',
        'new_stock' => 'integer',
    ];

    /**
     * Get the product that owns the stock movement.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who created the stock movement.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope a query to only include recent movements.
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope a query to only include movements of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the formatted movement description.
     */
    public function getDescriptionAttribute()
    {
        $typeMap = [
            'in' => 'Stok Masuk',
            'out' => 'Stok Keluar',
            'adjustment' => 'Penyesuaian Stok'
        ];

        $sourceMap = [
            'supplier' => 'dari Supplier',
            'store' => 'ke Toko',
            'adjustment' => 'Penyesuaian',
            'distribution' => 'Distribusi',
            'store_adjustment' => 'Penyesuaian Toko',
            'return_to_supplier' => 'Retur ke Supplier'
        ];

        return $typeMap[$this->type] . ' ' . ($sourceMap[$this->source] ?? $this->source);
    }

    /**
     * Get the store that owns the stock movement (if applicable).
     */
    public function store()
    {
        if ($this->reference_type === 'store') {
            return $this->belongsTo(Store::class, 'reference_id');
        }
        return null;
    }

    /**
     * Scope a query to only include store-level movements.
     */
    public function scopeStoreMovements($query, $storeId = null)
    {
        $query = $query->where('reference_type', 'store');

        if ($storeId) {
            $query->where('reference_id', $storeId);
        }

        return $query;
    }

    /**
     * Scope a query to only include store adjustments.
     */
    public function scopeStoreAdjustments($query, $storeId = null)
    {
        $query = $query->where('source', 'store_adjustment');

        if ($storeId) {
            $query->where('reference_id', $storeId);
        }

        return $query;
    }

    /**
     * Get formatted store information for admin dashboard.
     */
    public function getStoreInfoAttribute()
    {
        if ($this->reference_type === 'store' && $this->reference_id) {
            $store = Store::find($this->reference_id);
            return $store ? $store->name : 'Toko Tidak Diketahui';
        }
        return null;
    }

    /**
     * Get formatted movement type for display.
     */
    public function getFormattedTypeAttribute()
    {
        $typeMap = [
            'in' => 'Masuk',
            'out' => 'Keluar',
            'adjustment' => 'Penyesuaian'
        ];

        return $typeMap[$this->type] ?? $this->type;
    }

    /**
     * Get formatted source for display.
     */
    public function getFormattedSourceAttribute()
    {
        $sourceMap = [
            'supplier' => 'Supplier',
            'store' => 'Toko',
            'adjustment' => 'Penyesuaian Admin',
            'distribution' => 'Distribusi',
            'store_adjustment' => 'Penyesuaian Toko',
            'return_to_supplier' => 'Retur ke Supplier'
        ];

        return $sourceMap[$this->source] ?? $this->source;
    }

    /**
     * Check if this movement is a store-level adjustment.
     */
    public function isStoreAdjustment()
    {
        return $this->source === 'store_adjustment' && $this->reference_type === 'store';
    }

    /**
     * Get recent store movements for admin dashboard monitoring.
     */
    public static function getRecentStoreMovements($days = 7, $limit = 10)
    {
        return self::with(['product', 'creator'])
            ->where('source', 'store_adjustment')
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($movement) {
                $store = Store::find($movement->reference_id);
                $movement->store_name = $store ? $store->name : 'Toko Tidak Diketahui';
                return $movement;
            });
    }
}
