<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ReturnModel extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'returns';

    protected $fillable = [
        'distribution_id',
        'product_id',
        'store_id',
        'supplier_id',
        'quantity',
        'reason',
        'description',
        'status',
        'return_date',
        'approved_date',
        'completed_date',
        'requested_by',
        'approved_by',
        'admin_notes',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'reason' => 'string',
        'status' => 'string',
        'return_date' => 'date',
        'approved_date' => 'date',
        'completed_date' => 'date',
    ];

    /**
     * Get the distribution for the return.
     */
    public function distribution()
    {
        return $this->belongsTo(Distribution::class);
    }

    /**
     * Get the product for the return.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the store for the return (nullable for warehouse returns).
     */
    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    /**
     * Get the supplier for the return (nullable if not returned to supplier).
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * Get the user who requested the return.
     */
    public function requestedBy()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the admin who approved the return.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope a query to only include requested returns.
     */
    public function scopeRequested($query)
    {
        return $query->where('status', 'requested');
    }

    /**
     * Scope a query to only include approved returns.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include in-transit returns.
     */
    public function scopeInTransit($query)
    {
        return $query->where('status', 'in_transit');
    }

    /**
     * Scope a query to only include completed returns.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include rejected returns.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope a query to only include store returns.
     */
    public function scopeFromStore($query)
    {
        return $query->whereNotNull('store_id');
    }

    /**
     * Scope a query to only include warehouse returns.
     */
    public function scopeFromWarehouse($query)
    {
        return $query->whereNull('store_id');
    }

    /**
     * Scope a query to only include returns to suppliers.
     */
    public function scopeToSupplier($query)
    {
        return $query->whereNotNull('supplier_id');
    }

    /**
     * Check if return is from store.
     */
    public function isFromStore()
    {
        return !is_null($this->store_id);
    }

    /**
     * Check if return is from warehouse.
     */
    public function isFromWarehouse()
    {
        return is_null($this->store_id);
    }

    /**
     * Check if return goes to supplier.
     */
    public function isToSupplier()
    {
        return !is_null($this->supplier_id);
    }

    /**
     * Get the return source (store name or warehouse).
     */
    public function getSourceAttribute()
    {
        return $this->isFromStore() ? $this->store->name : 'Gudang Pusat';
    }

    /**
     * Get the return destination (supplier name or warehouse).
     */
    public function getDestinationAttribute()
    {
        return $this->isToSupplier() ? $this->supplier->name : 'Gudang Pusat';
    }

    /**
     * Get the reason in Indonesian.
     */
    public function getReasonInIndonesianAttribute()
    {
        $reasons = [
            'damaged' => 'Rusak',
            'expired' => 'Kadaluarsa',
            'defective' => 'Cacat',
            'overstock' => 'Kelebihan Stok',
            'shortage' => 'Kekurangan Pengiriman',
            'other' => 'Lainnya',
        ];

        return $reasons[$this->reason] ?? $this->reason;
    }

    /**
     * Get the status in Indonesian.
     */
    public function getStatusInIndonesianAttribute()
    {
        $statuses = [
            'requested' => 'Diminta',
            'approved' => 'Disetujui',
            'in_transit' => 'Dalam Perjalanan',
            'completed' => 'Selesai',
            'rejected' => 'Ditolak',
        ];

        return $statuses[$this->status] ?? $this->status;
    }
}
