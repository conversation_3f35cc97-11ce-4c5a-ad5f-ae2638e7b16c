<?php $__env->startSection('title', 'Detail Distribusi - Indah Berkah Abadi'); ?>

<?php $__env->startSection('content'); ?>
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Detail Distribusi</h1>
            <p class="user-dashboard-header-subtitle">Informasi lengkap distribusi dari gudang</p>
        </div>
        <div class="user-dashboard-header-actions">
            <a href="<?php echo e(route('user.deliveries')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali
            </a>
            <?php if(!$delivery->confirmed): ?>
            <form method="PATCH" action="<?php echo e(route('user.deliveries.confirm', $delivery->id)); ?>" style="display: inline;">
                <?php echo csrf_field(); ?>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center" onclick="return confirm('Konfirmasi penerimaan distribusi ini?')">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Konfirmasi Sekarang
                </button>
            </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- Distribution Status -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Status Distribusi</h2>
                    <p class="text-sm text-gray-600 mt-1">Status saat ini dari distribusi ini</p>
                </div>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold
                    <?php echo e($delivery->confirmed 
                        ? 'bg-green-100 text-green-800 border border-green-200 shadow-sm' 
                        : 'bg-yellow-100 text-yellow-800 border border-yellow-200 shadow-sm'); ?>">
                    <?php if($delivery->confirmed): ?>
                        <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        Dikonfirmasi
                    <?php else: ?>
                        <svg class="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3"></path>
                        </svg>
                        Menunggu Konfirmasi
                    <?php endif; ?>
                </span>
            </div>
        </div>
    </div>

    <!-- Distribution Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Information -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Informasi Produk</h2>
            </div>
            <div class="user-dashboard-card-content">
                <div class="space-y-4">
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Nama Produk</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">
                            <?php echo e($delivery->product ? $delivery->product->name : 'Produk tidak diketahui'); ?>

                        </p>
                    </div>
                    <?php if($delivery->confirmed && $delivery->received_quantity !== null): ?>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Jumlah Diterima</label>
                        <p class="text-lg font-semibold text-green-600 mt-1">
                            <?php echo e(number_format($delivery->received_quantity)); ?> unit
                        </p>
                        <?php if($delivery->received_quantity != $delivery->quantity): ?>
                        <p class="text-sm text-gray-500 mt-1">
                            Dari <?php echo e(number_format($delivery->quantity)); ?> unit yang didistribusikan
                        </p>
                        <?php endif; ?>
                    </div>
                    <?php else: ?>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Jumlah Distribusi</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">
                            <?php echo e(number_format($delivery->quantity)); ?> unit
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                            Belum dikonfirmasi
                        </p>
                    </div>
                    <?php endif; ?>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Tanggal Distribusi</label>
                        <p class="text-gray-900 mt-1">
                            <?php echo e($delivery->date_distributed ? $delivery->date_distributed->format('d M Y') : 'Belum dijadwalkan'); ?>

                        </p>
                    </div>
                    <?php if($delivery->confirmed && $delivery->notes): ?>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Catatan Penerimaan</label>
                        <div class="bg-gray-50 rounded-lg p-3 mt-1">
                            <p class="text-gray-900 text-sm"><?php echo e($delivery->notes); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Store Information -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Informasi Toko</h2>
            </div>
            <div class="user-dashboard-card-content">
                <div class="space-y-4">
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Nama Toko</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">
                            <?php echo e($delivery->store ? $delivery->store->name : 'Toko tidak diketahui'); ?>

                        </p>
                    </div>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Lokasi</label>
                        <p class="text-gray-900 mt-1">
                            <?php echo e($delivery->store ? $delivery->store->location : 'Lokasi tidak diketahui'); ?>

                        </p>
                    </div>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Tanggal Dibuat</label>
                        <p class="text-gray-900 mt-1">
                            <?php echo e($delivery->created_at ? $delivery->created_at->format('d M Y, H:i') : 'Tidak diketahui'); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Timeline Distribusi</h2>
            <p class="user-dashboard-card-description">Riwayat status distribusi</p>
        </div>
        <div class="user-dashboard-card-content">
            <div class="space-y-4">
                <div class="user-dashboard-form-group">
                    <ul class="space-y-6">
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">
                                        Distribusi dibuat pada 
                                        <time datetime="<?php echo e($delivery->created_at ? $delivery->created_at->toISOString() : ''); ?>">
                                            <?php echo e($delivery->created_at ? $delivery->created_at->format('d M Y, H:i') : 'Tidak diketahui'); ?>

                                        </time>
                                    </p>
                                </div>
                            </div>
                        </li>
                        <?php if($delivery->date_distributed): ?>
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">
                                        Distribusi dijadwalkan pada 
                                        <time datetime="<?php echo e($delivery->date_distributed->toISOString()); ?>">
                                            <?php echo e($delivery->date_distributed->format('d M Y')); ?>

                                        </time>
                                    </p>
                                </div>
                            </div>
                        </li>
                        <?php endif; ?>
                        <?php if($delivery->confirmed): ?>
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">
                                        Distribusi dikonfirmasi pada
                                        <time datetime="<?php echo e($delivery->confirmed_at ? $delivery->confirmed_at->toISOString() : ''); ?>">
                                            <?php echo e($delivery->confirmed_at ? $delivery->confirmed_at->format('d M Y, H:i') : 'Tidak diketahui'); ?>

                                        </time>
                                    </p>
                                    <?php if($delivery->received_quantity !== null && $delivery->received_quantity != $delivery->quantity): ?>
                                    <p class="text-xs text-gray-400 mt-1">
                                        Diterima <?php echo e(number_format($delivery->received_quantity)); ?> dari <?php echo e(number_format($delivery->quantity)); ?> unit
                                    </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </li>
                        <?php else: ?>
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">Menunggu konfirmasi penerimaan</p>
                                </div>
                            </div>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <?php if(!$delivery->confirmed): ?>
    <!-- Action Card -->
    <div class="user-dashboard-card bg-yellow-50 border-yellow-200">
        <div class="user-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-yellow-900">Konfirmasi Diperlukan</h3>
                    <p class="text-sm text-yellow-700 mt-1">
                        Distribusi ini belum dikonfirmasi. Silakan konfirmasi penerimaan untuk memperbarui stok toko.
                    </p>
                </div>
                <form method="PATCH" action="<?php echo e(route('user.deliveries.confirm', $delivery->id)); ?>" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center" onclick="return confirm('Konfirmasi penerimaan distribusi ini?')">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Konfirmasi Sekarang
                    </button>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/user/deliveries/show.blade.php ENDPATH**/ ?>