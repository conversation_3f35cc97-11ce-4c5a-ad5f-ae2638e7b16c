<?php $__env->startSection('title', 'Buat Distribusi - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Buat Distribusi'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Buat Distribusi Baru</h1>
                    <p class="text-gray-600 mt-1">Buat distribusi produk dari gudang pusat ke toko</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.distributions.index')); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <form action="<?php echo e(route('admin.distributions.store')); ?>" method="POST" class="space-y-6" id="distributionForm">
        <?php echo csrf_field(); ?>

        <!-- Basic Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Distribusi</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Store Selection -->
                    <div>
                        <label for="store_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Toko Tujuan <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <select
                                id="store_id"
                                name="store_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['store_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                required
                                <?php echo e($stores->count() == 0 ? 'disabled' : ''); ?>

                            >
                                <option value=""><?php echo e($stores->count() > 0 ? 'Pilih Toko Tujuan' : 'Tidak ada toko tersedia'); ?></option>
                                <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($store->id); ?>" <?php echo e(old('store_id', $selectedStoreId) == $store->id ? 'selected' : ''); ?>>
                                        <?php echo e($store->name); ?> - <?php echo e($store->location); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php if($stores->count() == 0): ?>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                <button type="button"
                                        onclick="window.open('<?php echo e(route('admin.users.create')); ?>', '_blank')"
                                        class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Tambah
                                </button>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php if($stores->count() == 0): ?>
                        <div class="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-orange-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                <div class="text-sm">
                                    <p class="font-medium text-orange-800 mb-1">Tidak Ada Toko Tujuan</p>
                                    <p class="text-orange-700 mb-2">
                                        Belum ada toko yang dapat menerima distribusi. Distribusi hanya dapat dibuat ke toko yang memiliki pengguna terdaftar.
                                    </p>
                                    <div class="flex flex-col sm:flex-row gap-2">
                                        <a href="<?php echo e(route('admin.users.create')); ?>"
                                           class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Tambah Toko Baru
                                        </a>
                                        <button type="button"
                                                onclick="window.location.reload()"
                                                class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                            </svg>
                                            Refresh Halaman
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <p class="mt-1 text-sm text-gray-600">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <?php echo e($stores->count()); ?> toko tersedia untuk distribusi (tidak termasuk Gedung Pusat)
                        </p>
                        <?php endif; ?>
                        <?php $__errorArgs = ['store_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Product Selection -->
                    <div>
                        <label for="product_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Produk <span class="text-red-500">*</span>
                        </label>
                        <select
                            id="product_id"
                            name="product_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['product_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required
                        >
                            <option value="">Pilih Produk</option>
                            <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <option value="<?php echo e($product->id); ?>" <?php echo e(old('product_id') == $product->id ? 'selected' : ''); ?>>
                                    <?php echo e($product->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <option value="" disabled>Belum ada produk tersedia</option>
                            <?php endif; ?>
                        </select>
                        <?php if($products->count() == 0): ?>
                        <p class="mt-1 text-sm text-orange-600">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            Belum ada produk yang terdaftar. <a href="<?php echo e(route('admin.products.create')); ?>" class="text-blue-600 hover:text-blue-800 underline">Tambah produk baru</a>
                        </p>
                        <?php endif; ?>
                        <?php $__errorArgs = ['product_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <!-- Quantity -->
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="quantity"
                            name="quantity"
                            value="<?php echo e(old('quantity', 1)); ?>"
                            min="1"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required
                        >
                        <?php $__errorArgs = ['quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Distribution Date -->
                    <div>
                        <label for="date_distributed" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Distribusi <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="date"
                            id="date_distributed"
                            name="date_distributed"
                            value="<?php echo e(old('date_distributed', date('Y-m-d'))); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 <?php $__errorArgs = ['date_distributed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                            required
                        >
                        <?php $__errorArgs = ['date_distributed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>
        </div>



        <!-- Form Actions -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-content">
                <div class="flex flex-col sm:flex-row gap-3">
                    <button 
                        type="submit" 
                        class="admin-dashboard-btn admin-dashboard-btn-primary"
                        id="submit-btn"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Buat Distribusi
                    </button>
                    <a 
                        href="<?php echo e(route('admin.distributions.index')); ?>" 
                        class="admin-dashboard-btn admin-dashboard-btn-secondary"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batal
                    </a>
                </div>
            </div>
        </div>
    </form>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('product_id');
    const storeSelect = document.getElementById('store_id');
    const quantityInput = document.getElementById('quantity');
    const submitBtn = document.getElementById('submit-btn');

    let availableStock = 0;
    const hasStores = <?php echo e($stores->count() > 0 ? 'true' : 'false'); ?>;

    // Disable form submission if no stores available
    if (!hasStores) {
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        submitBtn.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            Tidak Dapat Membuat Distribusi
        `;
    }

    // Function to check warehouse stock
    function checkWarehouseStock(productId) {
        if (!productId) {
            clearStockInfo();
            return;
        }

        fetch(`<?php echo e(route('admin.distributions.warehouse-stock', ':product')); ?>`.replace(':product', productId))
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                availableStock = data.available_stock;
                updateStockInfo();
                validateQuantity();
            })
            .catch(error => {
                console.error('Error fetching stock:', error);
                clearStockInfo();
            });
    }

    // Function to update stock info display
    function updateStockInfo() {
        // Remove existing stock info
        const existingInfo = document.getElementById('stock-info');
        if (existingInfo) {
            existingInfo.remove();
        }

        // Add stock info after quantity input
        const stockInfo = document.createElement('p');
        stockInfo.id = 'stock-info';
        stockInfo.className = 'mt-1 text-sm text-blue-600';
        stockInfo.innerHTML = `<svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>Stok tersedia di gudang: ${availableStock}`;

        quantityInput.parentNode.appendChild(stockInfo);
    }

    // Function to clear stock info
    function clearStockInfo() {
        const existingInfo = document.getElementById('stock-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        availableStock = 0;
    }

    // Function to validate quantity
    function validateQuantity() {
        // Don't validate if no stores available
        if (!hasStores) {
            return;
        }

        const quantity = parseInt(quantityInput.value) || 0;
        const storeSelected = storeSelect.value !== '';

        // Remove existing error
        const existingError = document.getElementById('quantity-stock-error');
        if (existingError) {
            existingError.remove();
        }

        // Check if store is selected
        if (!storeSelected) {
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
            return;
        }

        if (quantity > availableStock && availableStock > 0) {
            // Add error message
            const errorMsg = document.createElement('p');
            errorMsg.id = 'quantity-stock-error';
            errorMsg.className = 'mt-1 text-sm text-red-600';
            errorMsg.innerHTML = `<svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>Jumlah distribusi melebihi stok yang tersedia di gudang`;

            quantityInput.parentNode.appendChild(errorMsg);
            quantityInput.classList.add('border-red-500');
            submitBtn.disabled = true;
            submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            // Remove error styling
            quantityInput.classList.remove('border-red-500');
            submitBtn.disabled = false;
            submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }

    // Event listeners
    productSelect.addEventListener('change', function() {
        checkWarehouseStock(this.value);
    });

    quantityInput.addEventListener('input', function() {
        validateQuantity();
    });

    // Store selection validation
    if (hasStores) {
        storeSelect.addEventListener('change', function() {
            validateQuantity();
        });
    }

    // Initial check if product is pre-selected
    if (productSelect.value) {
        checkWarehouseStock(productSelect.value);
    }

    // Initial validation
    validateQuantity();
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/distributions/create.blade.php ENDPATH**/ ?>